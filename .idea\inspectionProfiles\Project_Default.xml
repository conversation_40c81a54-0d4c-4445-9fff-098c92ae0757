<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JupyterPackageInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="huggingface_hub" />
            <item index="1" class="java.lang.String" itemvalue="hydra-core" />
            <item index="2" class="java.lang.String" itemvalue="opencv_python" />
            <item index="3" class="java.lang.String" itemvalue="h5py" />
            <item index="4" class="java.lang.String" itemvalue="numpy" />
            <item index="5" class="java.lang.String" itemvalue="requests" />
            <item index="6" class="java.lang.String" itemvalue="torch" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="torchaudio" />
            <item index="9" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="10" class="java.lang.String" itemvalue="typing-extensions" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>